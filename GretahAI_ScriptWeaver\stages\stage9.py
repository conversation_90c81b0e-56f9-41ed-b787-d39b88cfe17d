"""
Stage 9: Script Browser and Comparison for GretahAI ScriptWeaver

This module provides an always-accessible script browsing and comparison utility.
It provides functionality for:
- Browsing all generated scripts from current and previous sessions
- Comparing different script versions (original vs optimized)
- Displaying script metadata and generation history
- Searching and filtering scripts by various criteria
- Downloading any script version
- Side-by-side diff views with syntax highlighting

Key Features:
- **Always Accessible**: Can be accessed at any time, regardless of workflow stage
- **Session Independent**: Browse scripts from previous work sessions
- **Empty State Handling**: Graceful UI when no scripts exist with helpful navigation
- **Utility Nature**: Functions as a standalone tool rather than a workflow step

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Proper workflow transitions for quick navigation

Functions:
    stage9_browse_scripts(state): Main Stage 9 function for script browsing and comparison
"""

import os
import logging
import streamlit as st
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from state_manager import StateStage

# Import helper functions
from core.script_browser_helpers import (
    filter_scripts_by_criteria,
    search_scripts_by_content,
    generate_script_comparison,
    format_script_metadata,
    get_script_statistics,
    create_download_filename
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage9")


def stage9_browse_scripts(state):
    """
    Stage 9: Script Browser and Comparison.

    This stage provides an independent, always-accessible script browsing and comparison utility.
    It loads scripts from persistent storage across all sessions, making it truly session-independent.
    Users can access this stage at any time regardless of workflow completion status.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>📜 Script Browser & History</h2>", unsafe_allow_html=True)
    st.markdown("*Browse and compare all generated scripts from current and previous sessions*")

    # Get all scripts including historical ones from persistent storage
    try:
        all_scripts = state.get_all_scripts_with_history()
    except Exception as e:
        logger.error(f"Failed to load scripts: {e}")
        all_scripts = state.script_history  # Fallback to session scripts

    # Check if we have any scripts in history
    if not all_scripts:
        st.info("📝 **Script Browser is Empty**")
        st.markdown("""
        **Welcome to the Script Browser!** This utility allows you to browse, compare, and download all generated test scripts.

        **To get started:**
        - 📁 Upload a CSV file and configure your test case (Stages 1-3)
        - 🔍 Detect UI elements and configure test data (Stages 4-5)
        - ⚙️ Generate your first test script (Stage 6+)

        **What you can do here:**
        - 🔍 Browse and search through all generated scripts
        - 📊 Compare different script versions side-by-side
        - 📥 Download scripts for external use
        - 📈 View script generation history and metadata
        """)

        # Provide navigation to start the workflow
        st.markdown("### 🚀 Quick Start")
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📁 Start New Project", use_container_width=True, type="primary"):
                state.advance_to(StateStage.STAGE1_UPLOAD, "User started new project from Script Browser")
                st.rerun()
                return
        with col2:
            if st.button("🎯 Select Test Case", use_container_width=True):
                state.advance_to(StateStage.STAGE3_CONVERT, "User navigated to test case selection from Script Browser")
                st.rerun()
                return
        with col3:
            if st.button("⚙️ Generate Scripts", use_container_width=True):
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to script generation from Script Browser")
                st.rerun()
                return
        return

    # Display script statistics overview
    with st.expander("📊 Script History Overview", expanded=True):
        try:
            stats = state.get_script_statistics()
        except Exception as e:
            logger.error(f"Failed to get script statistics: {e}")
            stats = get_script_statistics(all_scripts)

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Scripts", stats.get('total_scripts', 0))
        with col2:
            st.metric("Test Cases", len(stats.get('test_cases', {})))
        with col3:
            st.metric("Total Lines", f"{stats.get('total_lines', 0):,}")
        with col4:
            st.metric("Total Size", f"{stats.get('total_size', 0):,} chars")

        # Script type breakdown
        script_types = stats.get('script_types', {})
        if script_types:
            st.markdown("**Script Types:**")
            type_cols = st.columns(len(script_types))
            for i, (script_type, count) in enumerate(script_types.items()):
                with type_cols[i]:
                    st.metric(script_type.title(), count)

        # Show date range if available
        if stats.get('date_range'):
            start_date, end_date = stats['date_range']
            st.markdown(f"**Date Range:** {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        # Show session info
        current_session_count = len([s for s in all_scripts if s.get('session_id') == getattr(state._script_storage, 'session_id', None)])
        historical_count = len(all_scripts) - current_session_count
        if historical_count > 0:
            st.info(f"📚 Showing {current_session_count} scripts from current session and {historical_count} from previous sessions")

    # Filtering and Search Section
    with st.expander("🔍 Filter and Search Scripts", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            # Filter options
            st.markdown("**Filter Options:**")

            # Script type filter
            script_types = list(set(script.get('type', 'unknown') for script in all_scripts))
            selected_type = st.selectbox(
                "Script Type",
                options=['All'] + script_types,
                index=0,
                key="filter_script_type"
            )

            # Test case filter
            test_cases = list(set(script.get('test_case_id', 'unknown') for script in all_scripts if script.get('test_case_id')))
            selected_test_case = st.selectbox(
                "Test Case",
                options=['All'] + test_cases,
                index=0,
                key="filter_test_case"
            )

            # Step number filter
            step_numbers = list(set(script.get('step_no', '') for script in all_scripts if script.get('step_no')))
            step_numbers = [s for s in step_numbers if s]  # Remove empty strings
            selected_step = st.selectbox(
                "Step Number",
                options=['All'] + sorted(step_numbers),
                index=0,
                key="filter_step_no"
            )

        with col2:
            # Search options
            st.markdown("**Search Options:**")

            search_term = st.text_input(
                "Search in script content",
                placeholder="Enter search term...",
                key="search_term"
            )

            case_sensitive = st.checkbox(
                "Case sensitive search",
                key="case_sensitive_search"
            )

            # Date range filter
            st.markdown("**Date Range:**")
            date_filter = st.selectbox(
                "Filter by date",
                options=['All time', 'Last hour', 'Last 24 hours', 'Last week'],
                index=0,
                key="date_filter"
            )

    # Apply filters and search
    filtered_scripts = all_scripts.copy()

    # Apply type filter
    if selected_type != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            script_type=selected_type
        )

    # Apply test case filter
    if selected_test_case != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            test_case_id=selected_test_case
        )

    # Apply step filter
    if selected_step != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            step_no=selected_step
        )

    # Apply date filter
    if date_filter != 'All time':
        now = datetime.now()
        if date_filter == 'Last hour':
            start_date = now - timedelta(hours=1)
        elif date_filter == 'Last 24 hours':
            start_date = now - timedelta(days=1)
        elif date_filter == 'Last week':
            start_date = now - timedelta(weeks=1)
        else:
            start_date = datetime.min

        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            date_range=(start_date, now)
        )

    # Apply search
    if search_term:
        filtered_scripts = search_scripts_by_content(
            filtered_scripts,
            search_term,
            case_sensitive
        )

    # Display filtered results count
    if len(filtered_scripts) != len(all_scripts):
        st.info(f"📋 Showing {len(filtered_scripts)} of {len(all_scripts)} scripts")

    # Script Browser Section
    with st.expander("📜 Script Browser", expanded=True):
        if not filtered_scripts:
            st.warning("No scripts match the current filters.")
        else:
            # Sort scripts by timestamp (newest first)
            sorted_scripts = sorted(filtered_scripts, key=lambda x: x.get('timestamp', datetime.min), reverse=True)

            for i, script in enumerate(sorted_scripts):
                render_enhanced_script_card(script, i)

    # Script Comparison Section
    with st.expander("🔄 Script Comparison", expanded=False):
        if len(filtered_scripts) < 2:
            st.info("Need at least 2 scripts to perform comparison.")
        else:
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Select First Script:**")
                script1_options = [f"{s.get('type', 'Script')} - {s.get('test_case_id', 'Unknown')} ({s.get('timestamp', datetime.now()).strftime('%H:%M:%S')})"
                                 for s in filtered_scripts]
                script1_idx = st.selectbox(
                    "First script",
                    options=range(len(script1_options)),
                    format_func=lambda x: script1_options[x],
                    key="compare_script1"
                )

            with col2:
                st.markdown("**Select Second Script:**")
                script2_idx = st.selectbox(
                    "Second script",
                    options=range(len(script1_options)),
                    format_func=lambda x: script1_options[x],
                    key="compare_script2"
                )

            # Comparison type
            comparison_type = st.radio(
                "Comparison Type",
                options=["side_by_side", "unified"],
                format_func=lambda x: "Side by Side" if x == "side_by_side" else "Unified Diff",
                horizontal=True,
                key="comparison_type"
            )

            if st.button("🔄 Generate Comparison", use_container_width=True):
                if script1_idx != script2_idx:
                    script1 = filtered_scripts[script1_idx]
                    script2 = filtered_scripts[script2_idx]

                    comparison_html = generate_script_comparison(script1, script2, comparison_type)
                    st.components.v1.html(comparison_html, height=600, scrolling=True)
                else:
                    st.warning("Please select two different scripts for comparison.")

    # Independent Script Browser Footer
    st.markdown("---")
    st.markdown("### 📚 Script Browser Information")

    col1, col2 = st.columns(2)

    with col1:
        st.info("""
        **🔄 Always Accessible**: This Script Browser is available at any time, regardless of your current workflow stage.

        **📊 Persistent Storage**: All scripts are automatically saved and will be available in future sessions.
        """)

    with col2:
        st.info("""
        **🔍 Full History**: Browse scripts from all previous sessions and test cases.

        **⚡ Independent**: Use this tool without affecting your current workflow progress.
        """)

    # Optional workflow navigation (non-intrusive)
    with st.expander("🧭 Quick Workflow Navigation", expanded=False):
        st.markdown("*Optional: Jump to other workflow stages if needed*")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📁 Upload CSV (Stage 1)", use_container_width=True):
                logger.info("User navigated to Stage 1 from Script Browser")
                state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to Stage 1 from Script Browser")
                st.rerun()

        with col2:
            if st.button("🎯 Test Cases (Stage 3)", use_container_width=True):
                logger.info("User navigated to Stage 3 from Script Browser")
                state.advance_to(StateStage.STAGE3_CONVERT, "User navigated to Stage 3 from Script Browser")
                st.rerun()

        with col3:
            if st.button("⚙️ Generate Scripts (Stage 6)", use_container_width=True):
                logger.info("User navigated to Stage 6 from Script Browser")
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to Stage 6 from Script Browser")
                st.rerun()


def render_enhanced_script_card(script: Dict[str, Any], index: int):
    """
    Render an enhanced script card with optimization indicators and visual badges.

    Args:
        script: Script dictionary containing all script information
        index: Index of the script for unique key generation
    """
    # Extract script information
    script_id = script.get('id', f'script_{index}')
    script_type = script.get('type', 'unknown')
    test_case_id = script.get('test_case_id', 'Unknown')
    step_no = script.get('step_no', 'N/A')
    timestamp = script.get('timestamp', datetime.now())
    content = script.get('content', '')
    file_path = script.get('file_path', 'N/A')
    metadata = script.get('metadata', {})
    optimization_status = script.get('optimization_status', metadata.get('optimization_status', 'none'))

    # Format timestamp
    if isinstance(timestamp, str):
        try:
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except ValueError:
            timestamp = datetime.now()

    with st.container():
        # Enhanced header with visual badges
        col1, col2, col3 = st.columns([4, 1, 1])

        with col1:
            # Create enhanced badges with optimization indicators
            type_colors = {
                'step': '#2196F3',      # Blue
                'combined': '#4CAF50',   # Green
                'optimized': '#FF9800',  # Orange
                'test': '#9C27B0'        # Purple
            }
            type_color = type_colors.get(script_type, '#757575')

            # Build badges HTML
            badges_html = f"""
            <div style="display: flex; align-items: center; margin-bottom: 10px; flex-wrap: wrap; gap: 8px;">
                <span style="background-color: {type_color}; color: white; padding: 6px 14px;
                           border-radius: 20px; font-size: 13px; font-weight: bold;">
                    {script_type.upper()}
                </span>
            """

            # Add optimization status badge for optimized scripts
            if script_type == 'optimized':
                badges_html += """
                <span style="background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
                           color: white; padding: 5px 12px; border-radius: 15px;
                           font-size: 11px; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                    ⚡ OPTIMIZED
                </span>
                """
            elif optimization_status == 'optimized':
                badges_html += """
                <span style="background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
                           color: white; padding: 5px 12px; border-radius: 15px;
                           font-size: 11px; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                    ✨ ENHANCED
                </span>
                """

            # Add validation status badge if available
            validation_results = metadata.get('validation_results', {})
            if validation_results:
                quality_score = validation_results.get('quality_score', 0)
                if quality_score >= 80:
                    badges_html += """
                    <span style="background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
                               color: white; padding: 4px 10px; border-radius: 12px;
                               font-size: 10px; font-weight: bold;">
                        ✅ HIGH QUALITY
                    </span>
                    """
                elif quality_score >= 60:
                    badges_html += """
                    <span style="background: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);
                               color: white; padding: 4px 10px; border-radius: 12px;
                               font-size: 10px; font-weight: bold;">
                        ⚠️ MEDIUM QUALITY
                    </span>
                    """
                else:
                    badges_html += """
                    <span style="background: linear-gradient(135deg, #F44336 0%, #EF5350 100%);
                               color: white; padding: 4px 10px; border-radius: 12px;
                               font-size: 10px; font-weight: bold;">
                        ❌ NEEDS REVIEW
                    </span>
                    """

            badges_html += f"""
                <span style="font-weight: bold; font-size: 16px; margin-left: 10px; color: #333;">
                    Test Case: {test_case_id}
                </span>
            </div>
            """

            st.markdown(badges_html, unsafe_allow_html=True)

            # Show step information
            if step_no and step_no != 'N/A':
                st.caption(f"Step: {step_no}")
            else:
                st.caption("All Steps Combined")

        with col2:
            st.caption(f"Generated: {timestamp.strftime('%Y-%m-%d %H:%M')}")

        with col3:
            # View/Compare toggle
            view_key = f"view_{script_id}_{index}"
            if st.button("👁️ View", key=view_key):
                st.session_state[f"show_script_{index}"] = not st.session_state.get(f"show_script_{index}", False)

        # Show cross-reference information for optimized scripts
        if script_type == 'optimized' and metadata:
            original_script_path = metadata.get('original_script_path')
            script_source = metadata.get('script_source')

            if original_script_path or script_source:
                st.info(f"""
                🔗 **Optimization Source:**
                {f'Original: {os.path.basename(original_script_path)}' if original_script_path else ''}
                {f'Source: {script_source}' if script_source else ''}
                """)

        # Show script content if toggled
        if st.session_state.get(f"show_script_{index}", False):
            # Enhanced metadata display using checkbox instead of nested expander
            if metadata:
                # Use checkbox to create collapsible metadata section
                show_metadata_key = f"show_metadata_{script_id}_{index}"
                show_metadata = st.checkbox("📊 Show Script Details & Metadata",
                                          key=show_metadata_key,
                                          value=False)

                if show_metadata:
                    with st.container():
                        # Show optimization-specific metadata first
                        if script_type == 'optimized':
                            st.markdown("#### 🔧 Optimization Details")

                            optimization_timestamp = metadata.get('optimization_timestamp')
                            if optimization_timestamp:
                                st.write(f"**Optimization Time:** {optimization_timestamp}")

                            optimization_duration = metadata.get('optimization_duration')
                            if optimization_duration:
                                st.write(f"**Duration:** {optimization_duration:.2f} seconds")

                            # Show validation results if available
                            if validation_results:
                                st.markdown("#### 📋 Quality Validation")
                                col1, col2, col3 = st.columns(3)

                                with col1:
                                    quality_score = validation_results.get('quality_score', 0)
                                    st.metric("Quality Score", f"{quality_score}%")

                                with col2:
                                    syntax_valid = validation_results.get('syntax_valid', False)
                                    st.metric("Syntax Valid", "✅" if syntax_valid else "❌")

                                with col3:
                                    ready_for_execution = validation_results.get('ready_for_execution', False)
                                    st.metric("Ready to Run", "✅" if ready_for_execution else "❌")

                                # Show issues if any
                                issues = validation_results.get('issues_found', [])
                                if issues:
                                    st.markdown("**Issues Found:**")
                                    for issue in issues:
                                        severity = issue.get('severity', 'unknown')
                                        description = issue.get('description', 'No description')
                                        severity_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(severity, '⚪')
                                        st.write(f"{severity_icon} {description}")

                        # Show all other metadata
                        st.markdown("#### 📄 General Information")
                        for key, value in metadata.items():
                            if key not in ['optimization_timestamp', 'optimization_duration', 'validation_results']:
                                if isinstance(value, dict):
                                    st.json(value)
                                else:
                                    st.write(f"**{key.replace('_', ' ').title()}:** {value}")

            # Script content with syntax highlighting
            st.markdown("#### 📄 Script Content")
            st.code(content, language='python')

            # Action buttons
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button(f"📋 Copy to Clipboard", key=f"copy_{script_id}_{index}"):
                    st.success("Script content ready to copy!")
                    st.code(content, language='python')

            with col2:
                # Create download button
                filename = create_download_filename(script)
                st.download_button(
                    label="💾 Download Script",
                    data=content,
                    file_name=filename,
                    mime="text/plain",
                    key=f"download_{script_id}_{index}"
                )

            with col3:
                if file_path and file_path != 'N/A':
                    st.markdown(f"**File:** `{os.path.basename(file_path)}`")

        # Add separator
        st.divider()
